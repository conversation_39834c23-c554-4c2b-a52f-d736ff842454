<template>
  <view v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <view class="modal-content" @click.stop>
      <!-- 关闭按钮 -->
      <view class="close-button" @click="handleClose">
        <image src="/static/icons/close.svg" mode="aspectFit" class="close-icon" />
      </view>

      <!-- 产品信息 -->
      <view class="product-section">
        <view class="product-image">
          <image :src="productInfo.image" mode="aspectFill" class="product-img" />
        </view>
        <view class="product-details">
          <text class="product-name">{{ productInfo.name }}</text>
          <text class="product-description">{{ productInfo.description }}</text>
          <text class="product-price">¥{{ productInfo.price.toFixed(2) }}</text>
        </view>
      </view>

      <!-- 认养数量 -->
      <view class="form-item">
        <text class="form-label">认养数量</text>
        <view class="quantity-selector">
          <view class="quantity-btn" @click="decreaseQuantity">
            <view class="minus-line"></view>
          </view>
          <view class="quantity-input">
            <text class="quantity-text">{{ quantity }}</text>
          </view>
          <view class="quantity-btn" @click="increaseQuantity">
            <image src="/static/icons/plus.svg" mode="aspectFit" class="plus-icon" />
          </view>
        </view>
      </view>

      <!-- 邮费信息 -->
      <view class="form-item shipping-info">
        <view class="shipping-row">
          <text class="form-label">邮费</text>
          <text class="shipping-price">{{ freightPrice }} 元</text>
        </view>
        <text class="shipping-tip">温馨提示:支付成功后，待苹果成熟后将尽快配送到您的地址</text>
      </view>

      <!-- 收货地址 -->
      <view class="form-item" @click="selectAddress">
        <text class="form-label">收货地址</text>
        <view v-if="selectedAddress" class="address-info">
          <view class="address-line">
            <text class="contact-name">{{ selectedAddress.contactName }}</text>
            <text class="phone">{{ selectedAddress.phone }}</text>
          </view>
          <text class="address-details">{{ selectedAddress.province }}{{ selectedAddress.city }}{{ selectedAddress.district }}{{ selectedAddress.detailedAddress }}</text>
        </view>
        <text v-else class="form-value placeholder" style="margin-right: 0">选择收货地址</text>
        <image src="/static/icons/arrow-right.svg" mode="aspectFit" class="arrow-icon" />
      </view>

      <!-- 挂牌命名 -->
      <view class="form-item input-item">
        <text class="form-label">挂牌命名</text>
        <input
          class="form-input"
          v-model="treeName"
          placeholder="请认养的树起个名"
          placeholder-class="input-placeholder"
        />
      </view>

      <!-- 寄语 -->
      <view class="form-item input-item">
        <text class="form-label">寄语</text>
        <input
          class="form-input"
          v-model="message"
          placeholder="写一个寄语吧"
          placeholder-class="input-placeholder"
        />
      </view>

      <!-- 协议同意 -->
      <view class="form-item agreement" @click="toggleAgreement">
        <view class="checkbox-container">
          <image
            class="checkbox-icon"
            :src="isAgreed ? '/static/icons/checked.svg' : '/static/icons/unchecked.svg'"
            mode="aspectFit"
          />
        </view>
        <text class="agreement-text">
          <text class="normal-text">我已阅读并同意</text>
          <text class="link-text" @click.stop="viewAgreement">《认养协议》</text>
        </text>
      </view>

      <!-- 总计和支付 -->
      <view class="bottom-section">
        <view class="total-section">
          <text class="total-label">共计</text>
          <text class="total-price">¥{{ totalPrice.toFixed(2) }}</text>
        </view>
        <view class="pay-button" @click="handlePay" :class="{ 'loading': isPayLoading }">
          <text class="pay-text" v-if="!isPayLoading">立即支付</text>
          <text class="pay-text" v-else>支付中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getAddressList } from '@/api/address.js'
import { submitOrder, getCashierPayParams } from '@/api/order'
import { useUserStore } from '@/store/user'
import NP from 'number-precision'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productInfo: {
    type: Object,
    default: () => ({
      name: '',
      description: '',
      price: 0,
      image: ''
    })
  }
})

// Emits
const emit = defineEmits(['close', 'success'])

// 用户store
const userStore = useUserStore()

// 响应式数据
const quantity = ref(1)
const isAgreed = ref(false)
const treeName = ref('')
const message = ref('')
const selectedAddress = ref(null)
const isPayLoading = ref(false)

// 计算属性
const totalPrice = computed(() => {
  // 使用 number-precision 库避免浮点数精度问题
  const productTotal = NP.times(props.productInfo.price, quantity.value)
  return NP.plus(productTotal, freightPrice.value)
})

const freightPrice = computed(() => {
  return props.productInfo.freightPrice || 0
})

// 方法
const handleClose = () => {
  emit('close')
}

const handleOverlayClick = () => {
  emit('close')
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const increaseQuantity = () => {
  quantity.value++
}

const selectAddress = () => {
  uni.navigateTo({
    url: '/pages/address/list/list?from=select'
  })
}



const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value
}

const viewAgreement = () => {
  uni.navigateTo({
    url: '/pages/common/agreement/agreement'
  })
}

const handlePay = async () => {
  if (!isAgreed.value) {
    uni.showToast({
      title: '请先同意认养协议',
      icon: 'none'
    })
    return
  }

  if (!selectedAddress.value) {
    uni.showToast({
      title: '请选择收货地址',
      icon: 'none'
    })
    return
  }

  // 开始支付流程，显示loading
  isPayLoading.value = true

  try {
    // 1. 提交订单
    const orderRes = await submitOrder({
      addressId: selectedAddress.value.id,
      fruitTreeId: props.productInfo.id,
      fruitNum: quantity.value,
      freightPrice: freightPrice.value,
      labelName: treeName.value,
      wishMsg: message.value
    })

    if (orderRes.data) {
      // 2. 获取支付参数
      const paymentParams = await getCashierPayParams(orderRes.data)

      // 3. 拉起支付
      await uni.requestPayment({
        ...paymentParams.data,
        success: async () => {
          // 支付成功：通知父组件并关闭弹窗
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          })

          // 刷新用户信息（更新统计数据）
          try {
            await userStore.refreshUserInfo()
          } catch (error) {
            console.error('刷新用户信息失败:', error)
          }

          emit('success')
          emit('close')
        },
        fail: (err) => {
          // 支付失败或用户取消：只重置loading状态，保持弹窗打开
          isPayLoading.value = false
          console.error('支付失败或取消:', err)
          // 用户主动取消支付时不显示错误提示
          if (err.errMsg && !err.errMsg.includes('cancel')) {
            uni.showToast({
              title: '支付失败',
              icon: 'none'
            })
          }
        }
      })
    } else {
      throw new Error('创建订单失败')
    }
  } catch (error) {
    // 异常情况：重置loading状态，保持弹窗打开
    isPayLoading.value = false
    console.error('支付流程出错:', error)
    uni.showToast({
      title: error.message || '支付失败',
      icon: 'none'
    })
  }
}



// 获取默认地址
const fetchDefaultAddress = async () => {
  try {
    const res = await getAddressList({ pageNum: 1, pageSize: 100 }) // 获取所有地址
    const defaultAddress = res.rows.find(addr => addr.isDefault)
    if (defaultAddress) {
      selectedAddress.value = defaultAddress
    }
  } catch (error) {
    console.error('获取地址列表失败', error)
  }
}

onMounted(() => {
  // 监听地址选择事件
  uni.$on('addressSelected', (address) => {
    selectedAddress.value = address
  })
})

watch(
  () => props.visible,
  (newVal) => {
    if (newVal && !selectedAddress.value) {
      fetchDefaultAddress()
    }
  }
)
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #767676;
$text-light-gray: #999999;
$border-gray: #ebebeb;
$border-light-gray: #eeeeee;
$bg-mask: rgba(0, 0, 0, 0.5);

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 模态框遮罩
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $bg-mask;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

// 模态框内容
.modal-content {
  position: relative;
  width: 100%;
  background-color: $white-color;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: auto;
}

// 关闭按钮
.close-button {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  @include flex-center;
  z-index: 10;

  .close-icon {
    width: 32rpx;
    height: 32rpx;
  }
}

// 产品信息区域
.product-section {
  display: flex;
  padding: 40rpx 32rpx;
  gap: 32rpx;

  .product-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;

    .product-img {
      width: 100%;
      height: 100%;
    }
  }

  .product-details {
    flex: 1;
    display: flex;
    flex-direction: column;

    .product-name {
      font-size: 34rpx;
      font-weight: 500;
      line-height: 46rpx;
      color: $text-dark;
      margin-bottom: 18rpx;
    }

    .product-description {
      font-size: 30rpx;
      line-height: 35rpx;
      color: $text-light-gray;
      margin-bottom: auto;
    }

    .product-price {
      font-size: 40rpx;
      font-weight: 500;
      line-height: 47rpx;
      color: $primary-color;
      margin-top: 20rpx;
    }
  }
}

// 表单项
.form-item {
  @include flex-between;
  padding: 31rpx 32rpx;
  border-bottom: 1rpx solid $border-light-gray;

  .form-label {
    font-size: 32rpx;
    line-height: 38rpx;
    color: $text-dark;
    flex-shrink: 0;
    margin-right: 32rpx;
  }

  .form-value {
    font-size: 32rpx;
    line-height: 38rpx;
    color: $text-gray;
    flex: 1;
    text-align: right;
    margin-right: 16rpx;

    &.placeholder {
      color: $text-light-gray;
    }
  }

  .arrow-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 16rpx;
  }

  .address-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    text-align: left;

    .address-line {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .contact-name {
        font-size: 30rpx;
        font-weight: 500;
        color: $text-dark;
        margin-right: 16rpx;
      }

      .phone {
        font-size: 28rpx;
        color: $text-gray;
      }
    }

    .address-details {
      font-size: 28rpx;
      color: $text-gray;
      line-height: 1.4;
    }
  }

  // 输入框样式
  &.input-item {
    .form-input {
      flex: 1;
      font-size: 32rpx;
      line-height: 38rpx;
      color: $text-gray;
      text-align: right;
      background: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: $text-light-gray;
      }
    }

    .input-placeholder {
      color: $text-light-gray;
    }
  }
}

// 数量选择器
.quantity-selector {
  display: flex;
  align-items: center;

  .quantity-btn {
    width: 48rpx;
    height: 48rpx;
    border: 2rpx solid $border-gray;
    background-color: $white-color;
    @include flex-center;

    .minus-line {
      width: 24rpx;
      height: 4rpx;
      background-color: $primary-color;
    }

    .plus-icon {
      width: 24rpx;
      height: 24rpx;
    }
  }

  .quantity-input {
    width: 80rpx;
    height: 48rpx;
    border-top: 2rpx solid $border-gray;
    border-bottom: 2rpx solid $border-gray;
    background-color: $white-color;
    @include flex-center;

    .quantity-text {
      font-size: 32rpx;
      font-weight: 500;
      line-height: 38rpx;
      color: $text-dark;
    }
  }
}

// 邮费信息
.shipping-info {
  flex-direction: column;
  align-items: flex-start;

  .shipping-row {
    width: 100%;
    @include flex-between;
    margin-bottom: 16rpx;

    .shipping-price {
      font-size: 32rpx;
      line-height: 38rpx;
      color: $text-dark;
    }
  }

  .shipping-tip {
    font-size: 26rpx;
    line-height: 30rpx;
    color: $text-light-gray;
  }
}

// 协议同意
.agreement {
  align-items: flex-start;
  padding-top: 34rpx;

  .checkbox-container {
    margin-right: 19rpx;
    margin-top: 2rpx;

    .checkbox-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .agreement-text {
    flex: 1;
    line-height: 38rpx;

    .normal-text {
      font-size: 32rpx;
      color: $text-dark;
    }

    .link-text {
      font-size: 32rpx;
      color: $primary-color;
    }
  }
}

// 底部区域
.bottom-section {
  padding: 32rpx;

  .total-section {
    @include flex-between;
    margin-bottom: 52rpx;

    .total-label {
      font-size: 32rpx;
      line-height: 38rpx;
      color: $text-dark;
    }

    .total-price {
      font-size: 40rpx;
      font-weight: 500;
      line-height: 47rpx;
      color: $primary-color;
    }
  }

  .pay-button {
    width: 100%;
    height: 88rpx;
    background-color: $primary-color;
    border-radius: 44rpx;
    @include flex-center;

    &.loading {
      background-color: #999999;
      pointer-events: none;
    }

    .pay-text {
      font-size: 34rpx;
      font-weight: 700;
      line-height: 40rpx;
      color: $white-color;
    }
  }
}
</style>
